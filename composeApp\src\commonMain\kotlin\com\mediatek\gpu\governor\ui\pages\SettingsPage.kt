package com.mediatek.gpu.governor.ui.pages

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.Icon
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.mediatek.gpu.governor.viewmodel.AppViewModel
import top.yukonga.miuix.kmp.basic.*
import top.yukonga.miuix.kmp.extra.SuperSwitch
import top.yukonga.miuix.kmp.icon.MiuixIcons
import top.yukonga.miuix.kmp.icon.icons.*

@Composable
fun SettingsPage(viewModel: AppViewModel) {
    val isDarkTheme by viewModel.isDarkTheme.collectAsState()
    val followSystemTheme by viewModel.followSystemTheme.collectAsState()
    val language by viewModel.language.collectAsState()
    val logLevel by viewModel.logLevel.collectAsState()

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    SmallTitle(text = "设置")
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 深色模式跟随系统设置
                    SuperSwitch(
                        title = "深色模式跟随系统",
                        summary = "自动根据系统设置切换主题",
                        checked = followSystemTheme,
                        onCheckedChange = { viewModel.setFollowSystemTheme(it) }
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 手动主题切换（仅在不跟随系统时显示）
                    if (!followSystemTheme) {
                        SuperSwitch(
                            title = "深色模式",
                            summary = "手动切换深色/浅色主题",
                            checked = isDarkTheme,
                            onCheckedChange = { viewModel.toggleTheme() }
                        )

                        Spacer(modifier = Modifier.height(16.dp))
                    }
                    
                    // 语言设置
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = MiuixIcons.Language,
                            contentDescription = null,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "语言设置:",
                            style = androidx.compose.material3.MaterialTheme.typography.titleMedium
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        val languages = listOf(
                            "system" to "跟随系统",
                            "zh" to "中文",
                            "en" to "English"
                        )

                        languages.forEach { (value, label) ->
                            Button(
                                onClick = { viewModel.setLanguage(value) },
                                colors = if (language == value) {
                                    androidx.compose.material3.ButtonDefaults.buttonColors()
                                } else {
                                    androidx.compose.material3.ButtonDefaults.outlinedButtonColors()
                                }
                            ) {
                                Text(label)
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "修改语言设置后实时生效",
                        style = androidx.compose.material3.MaterialTheme.typography.bodySmall
                    )
                    Text(
                        text = "跟随系统将自动检测系统语言设置",
                        style = androidx.compose.material3.MaterialTheme.typography.bodySmall
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 日志等级设置
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = MiuixIcons.BugReport,
                            contentDescription = null,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "主日志等级:",
                            style = androidx.compose.material3.MaterialTheme.typography.titleMedium
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        val logLevels = listOf(
                            "debug" to "Debug",
                            "info" to "Info",
                            "warn" to "Warn",
                            "error" to "Error"
                        )

                        logLevels.forEach { (value, label) ->
                            Button(
                                onClick = { viewModel.setLogLevel(value) },
                                colors = if (logLevel == value) {
                                    androidx.compose.material3.ButtonDefaults.buttonColors()
                                } else {
                                    androidx.compose.material3.ButtonDefaults.outlinedButtonColors()
                                }
                            ) {
                                Text(label)
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "修改日志等级后实时生效",
                        style = androidx.compose.material3.MaterialTheme.typography.bodySmall
                    )
                    Text(
                        text = "设置为Debug级别将启用详细日志记录",
                        style = androidx.compose.material3.MaterialTheme.typography.bodySmall
                    )
                }
            }
        }
    }
}
