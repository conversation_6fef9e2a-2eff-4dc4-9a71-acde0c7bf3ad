package com.mediatek.gpu.governor.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.mediatek.gpu.governor.data.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class AppViewModel : ViewModel() {
    // 导航状态
    private val _currentPage = MutableStateFlow(NavigationItem.STATUS)
    val currentPage: StateFlow<NavigationItem> = _currentPage.asStateFlow()

    // 主题设置
    private val _isDarkTheme = MutableStateFlow(false)
    val isDarkTheme: StateFlow<Boolean> = _isDarkTheme.asStateFlow()

    private val _followSystemTheme = MutableStateFlow(true)
    val followSystemTheme: StateFlow<Boolean> = _followSystemTheme.asStateFlow()

    // 系统状态
    private val _systemStatus = MutableStateFlow(
        SystemStatus(
            isRunning = false,
            gameMode = false,
            moduleVersion = "加载中..."
        )
    )
    val systemStatus: StateFlow<SystemStatus> = _systemStatus.asStateFlow()

    // GPU配置
    private val _gpuConfigs = MutableStateFlow<List<GpuConfig>>(emptyList())
    val gpuConfigs: StateFlow<List<GpuConfig>> = _gpuConfigs.asStateFlow()

    // 游戏列表
    private val _gamesList = MutableStateFlow<List<String>>(emptyList())
    val gamesList: StateFlow<List<String>> = _gamesList.asStateFlow()

    // 日志内容
    private val _logContent = MutableStateFlow("加载中...")
    val logContent: StateFlow<String> = _logContent.asStateFlow()

    private val _currentLogType = MutableStateFlow(LogType.MAIN)
    val currentLogType: StateFlow<LogType> = _currentLogType.asStateFlow()

    // 设置
    private val _marginPercentage = MutableStateFlow(20)
    val marginPercentage: StateFlow<Int> = _marginPercentage.asStateFlow()

    private val _language = MutableStateFlow("system")
    val language: StateFlow<String> = _language.asStateFlow()

    private val _logLevel = MutableStateFlow("info")
    val logLevel: StateFlow<String> = _logLevel.asStateFlow()

    // 加载状态
    private val _isLoading = MutableStateFlow(true)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    init {
        loadInitialData()
    }

    fun navigateTo(page: NavigationItem) {
        _currentPage.value = page
    }

    fun toggleTheme() {
        _isDarkTheme.value = !_isDarkTheme.value
    }

    fun setFollowSystemTheme(follow: Boolean) {
        _followSystemTheme.value = follow
    }

    fun setLanguage(language: String) {
        _language.value = language
    }

    fun setLogLevel(level: String) {
        _logLevel.value = level
    }

    fun setMarginPercentage(percentage: Int) {
        _marginPercentage.value = percentage
    }

    fun switchLogType(logType: LogType) {
        _currentLogType.value = logType
        loadLogContent()
    }

    fun refreshLog() {
        loadLogContent()
    }

    fun addGpuConfig(config: GpuConfig) {
        val currentList = _gpuConfigs.value.toMutableList()
        currentList.add(config)
        _gpuConfigs.value = currentList
    }

    fun updateGpuConfig(index: Int, config: GpuConfig) {
        val currentList = _gpuConfigs.value.toMutableList()
        if (index in currentList.indices) {
            currentList[index] = config
            _gpuConfigs.value = currentList
        }
    }

    fun removeGpuConfig(index: Int) {
        val currentList = _gpuConfigs.value.toMutableList()
        if (index in currentList.indices) {
            currentList.removeAt(index)
            _gpuConfigs.value = currentList
        }
    }

    fun addGame(packageName: String) {
        val currentList = _gamesList.value.toMutableList()
        if (!currentList.contains(packageName)) {
            currentList.add(packageName)
            _gamesList.value = currentList
        }
    }

    fun removeGame(packageName: String) {
        val currentList = _gamesList.value.toMutableList()
        currentList.remove(packageName)
        _gamesList.value = currentList
    }

    private fun loadInitialData() {
        viewModelScope.launch {
            // 模拟加载数据
            _isLoading.value = false
            loadSystemStatus()
            loadGpuConfigs()
            loadGamesList()
            loadLogContent()
        }
    }

    private fun loadSystemStatus() {
        // 这里应该调用实际的API来获取系统状态
        // 暂时使用模拟数据
        _systemStatus.value = SystemStatus(
            isRunning = true,
            gameMode = false,
            moduleVersion = "v1.0.0"
        )
    }

    private fun loadGpuConfigs() {
        // 这里应该调用实际的API来获取GPU配置
        // 暂时使用模拟数据
        _gpuConfigs.value = listOf(
            GpuConfig(350000, 65000, 0),
            GpuConfig(400000, 62500, 1),
            GpuConfig(450000, 60000, 2)
        )
    }

    private fun loadGamesList() {
        // 这里应该调用实际的API来获取游戏列表
        // 暂时使用模拟数据
        _gamesList.value = listOf(
            "com.tencent.tmgp.sgame",
            "com.miHoYo.GenshinImpact",
            "com.tencent.tmgp.pubgmhd"
        )
    }

    private fun loadLogContent() {
        // 这里应该调用实际的API来获取日志内容
        // 暂时使用模拟数据
        val logType = _currentLogType.value
        _logContent.value = "正在加载${logType.displayName}...\n\n" +
                "[INFO] GPU调速器已启动\n" +
                "[INFO] 当前GPU频率: 350MHz\n" +
                "[INFO] 当前电压: 65000uV\n" +
                "[DEBUG] 检测到游戏应用启动\n" +
                "[INFO] 切换到游戏模式\n"
    }
}
